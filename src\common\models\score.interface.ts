import { ScoreActionType } from '../enums/score.enum';

// Interfaces que coinciden con los DTOs del backend
export interface ScoreResponseDto {
  id: number;
  userId: string;
  totalScore: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ScoreTransactionResponseDto {
  id: number;
  userId: string;
  changeAmount: number;
  actionType: string;
  description?: string;
  createdAt: Date;
}

export interface UserRankingDto {
  userId: string;
  userName: string;
  totalScore: number;
  rank: number;
}

export interface ScoreHistoryDto {
  score: ScoreResponseDto;
  transactions: ScoreTransactionResponseDto[];
  totalTransactions: number;
}

// DTOs para requests
export interface CreateScoreDto {
  userId: string;
}

export interface AddPointsDto {
  userId: string;
  points: number;
  actionType: ScoreActionType;
  description?: string;
}

export interface StoryActionDto {
  userId: string;
  storyTitle?: string;
}

export interface DailyLoginDto {
  userId: string;
}

export interface CustomActionDto {
  userId: string;
  actionType: ScoreActionType;
  description?: string;
  customPoints?: number;
}

// Query DTOs
export interface QueryScoreDto {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
}

export interface RankingQueryDto {
  page?: number;
  limit?: number;
}
