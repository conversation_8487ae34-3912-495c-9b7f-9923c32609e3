import {
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>,
  IonList,
  IonItem,
  IonAvatar,
  IonBadge,
  IonIcon,
  IonSpinner,
  IonText,
  <PERSON><PERSON><PERSON><PERSON>er,
  IonRefresherContent,
} from "@ionic/react";
import {
  trophyOutline,
  medalOutline,
  ribbonOutline,
  starOutline,
  refreshOutline,
} from "ionicons/icons";
import { UserRankingDto, useScoreRanking } from "../../hooks/score";
import Header from "../header/Header";
import "./Rankings.scss";

export const generateMockRankings = (): UserRankingDto[] => {
  const names = [
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON> Herrera",
    "<PERSON><PERSON><PERSON> Castro",
    "<PERSON><PERSON><PERSON> Alvarez",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "Renata Vá<PERSON>quez",
    "Agust<PERSON> Medina",
    "Micaela Bravo",
    "<PERSON><PERSON> Peña",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON> Peña",
    "<PERSON>guirre",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON> <PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON> <PERSON>",
    "<PERSON> <PERSON>",
    "<PERSON> <PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON> <PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON> <PERSON>",
    "<PERSON><PERSON>",
    "<PERSON> <PERSON><PERSON>",
    "<PERSON><PERSON> <PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON> <PERSON>",
    "Emilia Flores",
    "Joaquín Aguirre",
  ];

  // Generar puntos aleatorios entre 100 y 500
  const mockUsers: UserRankingDto[] = names.map((name) => ({
    userId: Math.random().toString(),
    userName: name,
    totalScore: Math.floor(Math.random() * 401) + 100, // 100-500
    rank: 0, // temporal, lo calculamos después
  }));

  // Ordenar de mayor a menor y asignar rank
  mockUsers.sort((a, b) => b.totalScore - a.totalScore);
  mockUsers.forEach((user, index) => {
    user.rank = index + 1;
  });

  return mockUsers;
};

const Rankings = () => {
  const { ranking, isLoading, refetch } = useScoreRanking();

  const displayRanking = ranking;

  // Función para manejar el refresh
  const handleRefresh = async (event: CustomEvent) => {
    try {
      // Refrescar solo los datos del ranking usando SWR
      await refetch();
    } catch (error) {
      console.error("Error refreshing rankings:", error);
    } finally {
      // Completar el refresh gesture
      event.detail.complete();
    }
  };

  const getRankingTitle = () => {
    return "Ranking General";
  };

  const getRankingIcon = (position: number) => {
    switch (position) {
      case 1:
        return { icon: trophyOutline, color: "#FFD700" }; // Oro
      case 2:
        return { icon: medalOutline, color: "#C0C0C0" }; // Plata
      case 3:
        return { icon: ribbonOutline, color: "#CD7F32" }; // Bronce
      default:
        return { icon: starOutline, color: "var(--ion-color-medium)" };
    }
  };

  return (
    <IonPage>
      <Header />

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent
            pullingIcon={refreshOutline}
            pullingText="Desliza para actualizar"
            refreshingSpinner="crescent"
            refreshingText="Actualizando rankings..."
          />
        </IonRefresher>

        <div className="rankings-page-header">
          <h3 className="rankings-title">{getRankingTitle()}</h3>
        </div>

        {/* Lista de rankings */}
        <div className="rankings-container">
          {isLoading ? (
            <div className="loading-container">
              <IonSpinner name="crescent" />
              <IonText>Cargando rankings...</IonText>
            </div>
          ) : (
            <IonList class="ion-list-styles">
              {displayRanking.map((entry) => {
                const rankIcon = getRankingIcon(entry.rank);
                return (
                  <IonItem key={entry.userId} className="ranking-item">
                    <div className="ranking-position">
                      <IonIcon
                        icon={rankIcon.icon}
                        style={{ color: rankIcon.color }}
                        className="position-icon"
                      />
                      <span className="position-number">#{entry.rank}</span>
                    </div>

                    <IonAvatar slot="start" className="user-avatar">
                      <div className="avatar-placeholder">
                        {entry.userName?.charAt(0).toUpperCase() || "U"}
                      </div>
                    </IonAvatar>

                    <div className="user-info">
                      <h3 className="user-name">
                        {entry.userName || "Usuario"}
                      </h3>
                      <p className="user-level">
                        Nivel {Math.floor(entry.totalScore / 50) + 1}
                      </p>
                    </div>

                    <div className="user-points" slot="end">
                      <IonBadge color="primary" className="points-badge">
                        {entry.totalScore} pts
                      </IonBadge>
                    </div>
                  </IonItem>
                );
              })}
            </IonList>
          )}

          {/* Mostrar indicador si estamos usando datos mock */}
          {ranking.length === 0 && !isLoading && (
            <div className="mock-data-indicator">
              <IonText color="medium">
                <p>📊 Mostrando datos de ejemplo</p>
              </IonText>
            </div>
          )}
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Rankings;
