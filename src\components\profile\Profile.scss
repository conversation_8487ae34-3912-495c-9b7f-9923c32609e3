/* Profile Container */
.profile-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  background: var(--background-color);
}

/* Profile Header */
.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: var(--surface-color);
  border-radius: 20px;
  border: 1px solid var(--border-color-light);
  box-shadow: 0 8px 32px var(--shadow-color-light);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  }
}

.profile-avatar {
  position: relative;
  flex-shrink: 0;

  .avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-on-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 700;
    box-shadow: 0 8px 24px var(--primary-color-tint);
    border: 4px solid var(--surface-color);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 12px 32px var(--primary-color-tint);
    }
  }

  .avatar-status {
    position: absolute;
    bottom: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background: var(--success-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid var(--surface-color);
    box-shadow: 0 2px 8px var(--shadow-color);

    ion-icon {
      font-size: 12px;
      color: white;
    }
  }
}

.profile-info {
  flex: 1;
  min-width: 0;

  .profile-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 4px 0;
    background: linear-gradient(135deg, var(--text-dark), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .profile-email {
    font-size: 1rem;
    color: var(--text-medium);
    margin: 0 0 12px 0;
    word-break: break-word;
  }

  .profile-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }
}

.stat-chip {
  --background: var(--primary-color-tint);
  --color: var(--primary-color);
  height: 32px;
  font-weight: 600;
  font-size: 0.9rem;
}

.level-chip {
  --background: var(--ion-color-success-tint);
  --color: white;

  ion-icon {
    font-size: 16px;
    margin-right: 4px;
    color: white;
  }
}

.status-badge {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 8px;
}

/* Cards */
.info-card,
.points-card,
.actions-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color-light);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px var(--shadow-color-light);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px var(--shadow-color);
  }

  ion-card-header {
    padding: 20px 20px 12px 20px;

    ion-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-dark);
      margin: 0;

      ion-icon {
        font-size: 1.3rem;
        color: var(--primary-color);
      }
    }
  }

  ion-card-content {
    padding: 20px;
  }
}

/* Info Grid */
.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--background-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color-light);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color-tint);
    transform: translateX(4px);
  }

  .info-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--primary-color-tint);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    ion-icon {
      font-size: 20px;
      color: var(--primary-color);
    }
  }

  .info-content {
    flex: 1;
    min-width: 0;

    .info-label {
      display: block;
      font-size: 0.85rem;
      font-weight: 600;
      color: var(--text-medium);
      margin-bottom: 2px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .info-value {
      display: block;
      font-size: 1rem;
      font-weight: 500;
      color: var(--text-dark);
      word-break: break-word;
    }
  }
}

/* Logout Button */
.logout-btn {
  --border-radius: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 28px rgba(244, 67, 54, 0.4);

    &::before {
      left: 100%;
    }
  }

  ion-icon {
    font-size: 20px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;

    /* Margen sutil entre los 3 elementos hijos en mobile */
    > * {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    /* Específicamente para ion-card */
    > ion-card {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 20px;

    .profile-info {
      .profile-name {
        font-size: 1.6rem;
      }

      .profile-stats {
        justify-content: center;
      }
    }
  }

  .profile-avatar .avatar-circle {
    width: 70px;
    height: 70px;
    font-size: 1.6rem;
  }

  .info-item,
  .points-stat-item {
    padding: 12px;
    gap: 12px;

    .info-icon,
    .stat-icon {
      width: 36px;
      height: 36px;

      ion-icon {
        font-size: 18px;
      }
    }
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 12px;
  }

  .profile-header {
    padding: 16px;

    .profile-info .profile-name {
      font-size: 1.4rem;
    }
  }

  .profile-avatar .avatar-circle {
    width: 60px;
    height: 60px;
    font-size: 1.4rem;
  }

  .info-card,
  .points-card,
  .actions-card {
    ion-card-header,
    ion-card-content {
      padding: 16px;
    }
  }

  .info-item,
  .points-stat-item {
    padding: 10px;
    gap: 10px;

    .info-content,
    .stat-content {
      .info-label,
      .stat-label {
        font-size: 0.8rem;
      }

      .info-value,
      .stat-value {
        font-size: 0.9rem;
      }
    }

    .info-icon,
    .stat-icon {
      width: 32px;
      height: 32px;

      ion-icon {
        font-size: 16px;
      }
    }
  }
}

/* Points Statistics Grid - usando el mismo estilo que info-grid */
.points-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.points-stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--background-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color-light);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color-tint);
    transform: translateX(4px);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--primary-color-tint);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    ion-icon {
      font-size: 20px;
      color: var(--primary-color);
    }
  }

  .stat-content {
    flex: 1;
    min-width: 0;

    .stat-label {
      display: block;
      font-size: 0.85rem;
      font-weight: 600;
      color: var(--text-medium);
      margin-bottom: 2px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-value {
      display: block;
      font-size: 1rem;
      font-weight: 500;
      color: var(--text-dark);
      word-break: break-word;
    }

    .stat-sublabel {
      display: block;
      font-size: 0.8rem;
      color: var(--text-medium);
      font-weight: 400;
      margin-top: 2px;
    }
  }
}

/* Progress bar dentro de stat-content */
.stat-content .progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 6px 0 4px 0;

  .progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
      border-radius: 3px;
      transition: width 0.3s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .progress-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
    min-width: 30px;
    text-align: right;
  }
}

/* Tema claro - mejor contraste para la barra de progreso */
body:not(.dark-theme) .stat-content .progress-container .progress-bar {
  background: #e8e8e8;
  border: 1px solid #d0d0d0;
}

.points-actions {
  margin-top: 20px;
  padding-top: 20px;
  display: flex;
  justify-content: center;
}

.points-action-btn {
  --border-radius: 16px;
  --padding-start: 20px;
  --padding-end: 20px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  --border-color: var(--primary-color);
  --color: var(--primary-color);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    --background: var(--primary-color-tint);
  }
}

/* Dark theme adaptations */
.dark-theme {
  .points-stat-item {
    background: var(--background-secondary);
    border-color: var(--border-color-light);

    .stat-icon {
      background: var(--primary-color-tint);
    }
  }

  .stat-content .progress-container .progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .points-actions {
    border-top-color: var(--border-color-light);
  }
}
