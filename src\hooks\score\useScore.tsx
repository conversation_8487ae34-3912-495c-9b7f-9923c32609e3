import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useFetch } from "../useFetch";
import {
  ScoreResponseDto,
  ScoreTransactionResponseDto,
  UserRankingDto,
  CreateScoreDto,
  AddPointsDto,
  StoryActionDto,
  DailyLoginDto,
  RankingQueryDto,
} from "../../common/models/score.interface";

// Interfaz del contexto completo
interface ScoreContextType {
  // Gestión básica de scores
  addPoints: (
    addPointsDto: AddPointsDto
  ) => Promise<{
    score: ScoreResponseDto;
    transaction: ScoreTransactionResponseDto;
  } | null>;
  getUserScore: (userId: string) => Promise<ScoreResponseDto | null>;
  // Rankings y estadísticas
  getRanking: (query?: RankingQueryDto) => Promise<UserRankingDto[] | null>;
  // Acciones específicas
  addPointsForStoryCreation: (
    userId: string,
    storyTitle?: string
  ) => Promise<boolean>;
  addPointsForStoryCompletion: (
    userId: string,
    storyTitle?: string
  ) => Promise<boolean>;
  addPointsForDailyLogin: (userId: string) => Promise<boolean>;
  // Estado
  isLoading: boolean;
  error: string | null;
}

// Contexto para el sistema de puntos
const ScoreContext = createContext<ScoreContextType | null>(null);

// Provider para el sistema de puntos
interface ScoreProviderProps {
  children: ReactNode;
}

export const ScoreProvider = ({ children }: ScoreProviderProps) => {
  const { post, get } = useFetch();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Agregar puntos a un usuario
  const addPoints = useCallback(
    async (
      addPointsDto: AddPointsDto
    ): Promise<{
      score: ScoreResponseDto;
      transaction: ScoreTransactionResponseDto;
    } | null> => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await post<
          { score: ScoreResponseDto; transaction: ScoreTransactionResponseDto },
          AddPointsDto
        >("score/add-points", addPointsDto);
        return response;
      } catch (err) {
        console.error("Error adding points:", err);
        setError("Error al agregar puntos");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [post]
  );

  // Obtener el score de un usuario específico
  const getUserScore = useCallback(
    async (userId: string): Promise<ScoreResponseDto | null> => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await get<ScoreResponseDto>(`score/user/${userId}`);
        return response;
      } catch (err) {
        console.error("Error getting user score:", err);
        setError("Error al obtener score del usuario");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [get]
  );

  // 6. Obtener ranking de usuarios
  const getRanking = useCallback(
    async (query?: RankingQueryDto): Promise<UserRankingDto[] | null> => {
      setIsLoading(true);
      setError(null);

      try {
        const queryParams = new URLSearchParams();
        if (query?.page) queryParams.append("page", query.page.toString());
        if (query?.limit) queryParams.append("limit", query.limit.toString());

        const url = `score/ranking${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
        const response = await get<UserRankingDto[]>(url);
        return response;
      } catch (err) {
        console.error("Error getting ranking:", err);
        setError("Error al obtener ranking");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [get]
  );

  // 8. Funciones de utilidad para acciones específicas
  const addPointsForStoryCreation = useCallback(
    async (userId: string, storyTitle?: string): Promise<boolean> => {
      setIsLoading(true);
      setError(null);

      try {
        await post<{ message: string }, StoryActionDto>(
          "score/actions/story-created",
          {
            userId,
            storyTitle,
          }
        );
        return true;
      } catch (err) {
        console.error("Error adding points for story creation:", err);
        setError("Error al agregar puntos por crear historia");
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [post]
  );

  const addPointsForStoryCompletion = useCallback(
    async (userId: string, storyTitle?: string): Promise<boolean> => {
      setIsLoading(true);
      setError(null);

      try {
        await post<{ message: string }, StoryActionDto>(
          "score/actions/story-completed",
          {
            userId,
            storyTitle,
          }
        );
        return true;
      } catch (err) {
        console.error("Error adding points for story completion:", err);
        setError("Error al agregar puntos por completar historia");
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [post]
  );

  const addPointsForDailyLogin = useCallback(
    async (userId: string): Promise<boolean> => {
      setIsLoading(true);
      setError(null);

      try {
        await post<{ message: string }, DailyLoginDto>(
          "score/actions/daily-login",
          {
            userId,
          }
        );
        return true;
      } catch (err) {
        console.error("Error adding points for daily login:", err);
        setError("Error al agregar puntos por login diario");
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [post]
  );

  const value: ScoreContextType = {
    addPoints,
    getUserScore,
    getRanking,
    addPointsForStoryCreation,
    addPointsForStoryCompletion,
    addPointsForDailyLogin,
    isLoading,
    error,
  };

  return (
    <ScoreContext.Provider value={value}>{children}</ScoreContext.Provider>
  );
};

// Hook para usar el contexto de score
export const useScore = () => {
  const context = useContext(ScoreContext);
  if (!context) {
    throw new Error("useScore must be used within a ScoreProvider");
  }
  return context;
};
