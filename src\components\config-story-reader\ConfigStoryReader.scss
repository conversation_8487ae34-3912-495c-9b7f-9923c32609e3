/* ConfigStoryReader - Estilos limpios y modernos */

.menu-custom {
  --background: var(--background-color);
  --width: 300px;
  box-shadow: 0 4px 20px var(--shadow-color);
}

.header-menu {
  --background: var(--background-color);
  --color: var(--text-dark);
  --padding-top: 30px;
  --padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color-light);
}

.menu-content {
  --background: var(--background-color);
  --padding-top: 25px;
  --padding-start: 15px;
  --padding-end: 15px;
  --padding-bottom: 15px;
}

.menu-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  text-align: center;
}

.menu-subtitle {
  font-size: 0.9rem;
  color: var(--text-medium);
  margin: 5px 0 0;
  text-align: center;
}

.config-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-dark);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 10px 0;
  padding: 0 15px;
  display: flex;
  align-items: center;
  gap: 8px;

  ion-icon {
    font-size: 1rem;
  }
}


