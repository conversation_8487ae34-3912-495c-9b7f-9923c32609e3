{"name": "storytale", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test.e2e": "cypress run", "test.unit": "vitest", "lint": "eslint", "format": "prettier --write .", "build:android": "node scripts/buildAndroid.js"}, "dependencies": {"@boengli/capacitor-fullscreen": "^0.0.19", "@capacitor-community/text-to-speech": "^6.0.0", "@capacitor/android": "^7.1.0", "@capacitor/app": "7.0.0", "@capacitor/core": "^7.1.0", "@capacitor/haptics": "7.0.0", "@capacitor/ios": "^7.4.3", "@capacitor/keyboard": "7.0.0", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "7.0.0", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "firebase": "^11.4.0", "ionicons": "^7.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-spinners": "^0.17.0", "sass": "^1.89.1", "swr": "^2.3.6"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "7.0.1", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.0.1", "cypress": "^13.5.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^22.1.0", "prettier": "3.5.3", "terser": "^5.4.0", "typescript": "^5.1.6", "typescript-eslint": "^8.24.0", "vite": "^6.3.5", "vitest": "^3.2.0"}, "description": "An Ionic project"}