import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import { SWRConfig } from "swr";

const container = document.getElementById("root");
const root = createRoot(container!);
root.render(
  <React.StrictMode>
    <SWRConfig
      value={{
        revalidateOnFocus: false, // No re-fetch al volver a la app
        revalidateOnReconnect: false, // No re-fetch al reconectar la red
        dedupingInterval: 1000 * 60 * 30, // 30 minutos
      }}
    >
      <App />
    </SWRConfig>
  </React.StrictMode>
);
