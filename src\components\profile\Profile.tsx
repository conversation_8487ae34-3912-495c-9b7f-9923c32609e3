import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonButton,
  IonChip,
  IonBadge
} from '@ionic/react';
import {
  person,
  mail,
  logOut,
  star,
  calendar,
  shield,
  trophyOutline,
  ribbonOutline,
  medalOutline,
  barChartOutline
} from 'ionicons/icons';
import { useUser } from '../../hooks/useUser';
import { useAuth } from '../../hooks/useAuth';
import { useUserScore } from '../../hooks/score';
import './Profile.scss';

import Header from '../header/Header';

export default function Profile() {
  const { user } = useUser();
  const { logout } = useAuth();
  const { totalScore } = useUserScore();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  // Calcular progreso al siguiente nivel
  const calculateProgress = (score: number) => {
    const pointsPerLevel = 50;
    const currentLevel = Math.floor(score / pointsPerLevel) + 1;
    const pointsInCurrentLevel = score % pointsPerLevel;
    const progressPercentage = (pointsInCurrentLevel / pointsPerLevel) * 100;
    const pointsToNextLevel = pointsPerLevel - pointsInCurrentLevel;

    return {
      currentLevel,
      progressPercentage: Math.round(progressPercentage),
      pointsToNextLevel
    };
  };

  const progress = calculateProgress(totalScore);

  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No disponible';
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'No disponible';
    }
  };

  // Helper function to get user initials
  const getUserInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <IonPage>
      <Header />
      <IonContent>
        <div className="profile-container">
          {/* Profile Header */}
          <div className="profile-header">
            <div className="profile-avatar">
              <div className="avatar-circle">
                {getUserInitials(user?.name)}
              </div>
              <div className="avatar-status">
                <IonIcon icon={shield} />
              </div>
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{user?.name || 'Usuario Invitado'}</h1>
              <p className="profile-email">{user?.email || 'Email no disponible'}</p>
              <div className="profile-stats">
                <IonChip className="stat-chip">
                  <IonIcon icon={star} />
                  <span>{totalScore} Puntos</span>
                </IonChip>
                <IonChip className="stat-chip level-chip">
                  <IonIcon icon={trophyOutline} />
                  <span>Nivel {progress.currentLevel}</span>
                </IonChip>
                <IonBadge className="status-badge" color="success">Activo</IonBadge>
              </div>
            </div>
          </div>

          {/* User Information Card */}
          <IonCard className="info-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={person} />
                Detalles de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="info-grid">
                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={person} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Nombre Completo</span>
                    <span className="info-value">{user?.name || 'No disponible'}</span>
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={mail} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Dirección de Email</span>
                    <span className="info-value">{user?.email || 'No disponible'}</span>
                  </div>
                </div>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Points Statistics Card */}
          <IonCard className="points-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={barChartOutline} />
                Estadísticas de Puntos
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="points-grid">
                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={star} color="warning" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Puntos Totales</span>
                    <span className="stat-value">{totalScore}</span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={trophyOutline} color="primary" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Nivel Actual</span>
                    <span className="stat-value">Nivel {progress.currentLevel}</span>
                    <span className="stat-sublabel">
                      {progress.currentLevel === 1 ? 'Principiante' :
                       progress.currentLevel <= 3 ? 'Novato' :
                       progress.currentLevel <= 5 ? 'Intermedio' :
                       progress.currentLevel <= 8 ? 'Avanzado' : 'Experto'}
                    </span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={ribbonOutline} color="success" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Progreso al Siguiente Nivel</span>
                    <div className="progress-container">
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${progress.progressPercentage}%` }}
                        ></div>
                      </div>
                      <span className="progress-text">{progress.progressPercentage}%</span>
                    </div>
                    <span className="stat-sublabel">
                      {progress.pointsToNextLevel} puntos para el siguiente nivel
                    </span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={medalOutline} color="tertiary" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Ranking General</span>
                    <span className="stat-value">Posición #1</span>
                    <span className="stat-sublabel">¡Eres el mejor!</span>
                  </div>
                </div>
              </div>

              <div className="points-actions">
                <IonButton
                  fill="outline"
                  size="small"
                  routerLink="/tabs/rankings"
                  className="points-action-btn"
                >
                  <IonIcon icon={trophyOutline} slot="start" />
                  Ver Rankings
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Actions Card */}
          <IonCard className="actions-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={logOut} />
                Acciones de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonButton
                className="action-button logout-btn"
                expand="block"
                fill="solid"
                color="danger"
                onClick={handleLogout}
              >
                <IonIcon icon={logOut} slot="start" />
                Cerrar Sesión
              </IonButton>
            </IonCardContent>
          </IonCard>
        </div>
      </IonContent>
    </IonPage>
  );
}
