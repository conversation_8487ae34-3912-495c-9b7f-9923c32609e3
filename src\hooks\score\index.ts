// Exportar todos los hooks relacionados con score
export { useScore, ScoreProvider } from './useScore';
export { useUserScore } from './useUserScore';
export { useScoreActions } from './useScoreActions';
export { useScoreRanking } from './useScoreRanking';

// Re-exportar tipos e interfaces comunes
export type {
  ScoreResponseDto,
  ScoreTransactionResponseDto,
  UserRankingDto,
  AddPointsDto,
  StoryActionDto,
  DailyLoginDto,
  RankingQueryDto,
} from '../../common/models/score.interface';

export { ScoreActionType, SCORE_POINTS } from '../../common/enums/score.enum';