import {
  IonContent,
  IonHeader,
  IonMenu,
  IonTitle,
  IonToolbar,
  IonItem,
  IonLabel,
  IonRange,
  IonIcon,
  IonList,
} from "@ionic/react";
import "./ConfigStoryReader.scss";
import { Capacitor } from "@capacitor/core";
import { speedometerOutline, textOutline, bookOutline } from "ionicons/icons";
import { useReaderSettings } from "../../hooks/useReaderSettings";
import { TextToSpeech } from "@capacitor-community/text-to-speech";

export default function ConfigStoryReader() {
  const { settings, updateReadingSpeed, updateFontSize } = useReaderSettings();

  // Función para verificar si TTS está disponible
  const isTTSAvailable = (): boolean => {
    const platform = Capacitor.getPlatform();
    if (platform === "android" || platform === "ios") {
      return true; // TTS nativo disponible en mobile
    }
    // En web, verificar si Speech API está disponible
    return "speechSynthesis" in window;
  };

  const handleMenuWillOpen = async () => {
    // Pausar el reproductor cuando se abra el menú
    if (isTTSAvailable()) {
      try {
        TextToSpeech.stop();
      } catch (error) {
        console.warn("Error al pausar TTS:", error);
      }
    }
  };

  // Handlers para los cambios en los controles
  const handleReadingSpeedChange = (event: CustomEvent) => {
    updateReadingSpeed(event.detail.value);
  };

  const handleFontSizeChange = (event: CustomEvent) => {
    updateFontSize(event.detail.value);
  };

  return (
    <IonMenu
      side="end"
      contentId="main-content"
      className="menu-custom"
      onIonWillOpen={handleMenuWillOpen}
    >
      <IonHeader>
        <IonToolbar className="header-menu">
          <IonTitle className="menu-title">Reading Settings</IonTitle>
          <p className="menu-subtitle ion-padding-horizontal">
            Customize your reading experience
          </p>
        </IonToolbar>
      </IonHeader>

      <IonContent className="menu-content">
        <div className="config-section">
          <div className="section-title">
            <IonIcon icon={speedometerOutline} />
            Audio Settings
          </div>
          <IonList lines="none">
            <IonItem className="menu-item">
              <IonIcon icon={speedometerOutline} slot="start" />
              <IonLabel>
                <h2>Reading Speed</h2>
                <p>Adjust speech rate ({settings.readingSpeed.toFixed(1)}x)</p>
              </IonLabel>
              <IonRange
                min={0.5}
                max={2}
                step={0.1}
                value={settings.readingSpeed}
                color="primary"
                onIonChange={handleReadingSpeedChange}
              ></IonRange>
            </IonItem>
          </IonList>
        </div>

        <div className="config-section">
          <div className="section-title">
            <IonIcon icon={bookOutline} />
            Display Settings
          </div>
          <IonList lines="none">
            <IonItem className="menu-item">
              <IonIcon icon={textOutline} slot="start" />
              <IonLabel>
                <h2>Font Size</h2>
                <p>Adjust text size ({settings.fontSize.toFixed(1)}x)</p>
              </IonLabel>
              <IonRange
                min={0.8}
                max={1.5}
                step={0.1}
                value={settings.fontSize}
                color="primary"
                onIonChange={handleFontSizeChange}
              ></IonRange>
            </IonItem>
          </IonList>
        </div>
      </IonContent>
    </IonMenu>
  );
}
