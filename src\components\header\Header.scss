.custom-toolbar-padding {
  padding-left: 16px;
  padding-right: 16px;
}

.centered-title {
  padding: 0 !important;
  margin: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.header-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  width: 100%;
  height: 56px;
}

.header-grid-fix {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  width: 100%;
  height: 56px;
}

.header-col {
  display: flex;
  align-items: center;
  height: 100%;
}

.user-name {
  justify-content: flex-start;
  padding-left: 16px;
  width: 100%;
}

.points-center {
  justify-content: center;
}

.notification-icon-outer {
  justify-content: flex-end;
  padding-right: 16px;
}

.notification-icon-container {
  position: relative;
  display: flex;
  align-items: center;
}

.notification-icon-container-fix {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  position: relative;
}

.notification-icon {
  font-size: 24px;
  color: var(--text-dark);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -8px;
  font-size: 10px;
}

.notification-badge-fix {
  position: absolute;
  top: -8px;
  right: 0px;
  font-size: 12px;
}

.user-name-text {
  font-weight: 500;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-dark) !important;
}

.points-icon {
  font-size: 20px;
  margin-right: 4px;
  vertical-align: middle;
  margin-bottom: 3px;
}

.points-value {
  font-weight: 700;
  font-size: 18px;
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0;
  margin: 0;
}

.user-name-container,
.notification-icon-outer {
  min-width: 110px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 44px;
  padding-left: 16px;
}

.notification-icon {
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: var(--primary-color);
  }
}

.theme-container {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.theme-toggle-btn {
  --background: transparent;
  --box-shadow: none;
  --color: var(--primary-color);
  padding: 0;
  min-width: 32px;
  min-height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:hover {
    --background: var(--primary-color-tint);
    transform: scale(1.05);
  }

  ion-icon {
    font-size: 18px;
    transition: transform 0.3s ease;
  }

  &:active ion-icon {
    transform: rotate(180deg);
  }
}
