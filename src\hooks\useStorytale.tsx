import { useCallback, useState } from "react";
import useS<PERSON>, { mutate } from "swr";

import { useFetch } from "./useFetch";
import {
  ICreateStory,
  IGenres,
  IStorytale,
} from "../common/models/storytale.interface";
import { useUser } from "./useUser";
import { useBusy } from "./useBusy";
import useToast from "./useToast";
import { useScoreActions } from "./score";

export const useStorytale = () => {
  const { get, post, del } = useFetch();
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const { setIsBusy, setIsLoadingStories } = useBusy();
  const { showSuccess, showError } = useToast();
  const { addStoryCreationPoints } = useScoreActions();

  const userId = user?.id;

  // SWR fetcher para historias del usuario
  const fetchStories = useCallback(async () => {
    if (!userId) return [];
    return (await get(`storytale/storytales/user/${userId}`)) as IStorytale[];
  }, [get, userId]);

  const {
    data: stories = [],
    error: storiesError,
    isLoading,
  } = useSWR<IStorytale[]>(
    userId ? `/storytales/${userId}` : null,
    fetchStories
  );

  // SWR fetcher para géneros
  const fetchGenres = useCallback(async () => {
    return (await get("storytale/genres")) as IGenres[];
  }, [get]);

  const { data: genres = [], error: genresError } = useSWR<IGenres[]>(
    "/storytale/genres",
    fetchGenres
  );

    // Crear cuento
  const createStory = async (storyData: ICreateStory) => {
    setIsBusy(true);
    try {
      const response = await post("storytale-creator", storyData);
      showSuccess("¡Cuento creado con éxito!");

      if (response && userId) {
        await addStoryCreationPoints(storyData.title);

        // Refrescar cache de historias
        mutate(`/storytales/${userId}`);
      }
    } catch (err) {
      showError("Error al crear el cuento.");
      console.error(err);
    } finally {
      setIsBusy(false);
    }
  };

  // Eliminar cuento
  const deleteStorytale = async (id: number) => {
    try {
      await del(`storytale/${id}`);
      showSuccess("¡Cuento eliminado con éxito!");

      // Actualizar cache de SWR
      if (userId) {
        mutate(
          `/storytales/${userId}`,
          stories.filter((story) => story.id !== id),
          false // no revalida automáticamente
        );
      }
    } catch (err) {
      showError("Error al eliminar el cuento.");
      console.error(err);
    }
  };

  return {
    stories,
    error,
    createStory,
    genres,
    deleteStorytale,
    isLoading
  };
};
