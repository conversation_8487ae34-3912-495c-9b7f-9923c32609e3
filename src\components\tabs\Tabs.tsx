import {
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON>abBar,
  <PERSON><PERSON><PERSON><PERSON>utton,
  IonRouterOutlet,
  IonIcon,
  IonLabel
} from "@ionic/react";
import { Redirect, Route } from "react-router-dom";
import { library, addCircle, person, trophyOutline } from "ionicons/icons";
import StorytaleList from "../storytale-list/StorytaleList";
import StorytaleCreation from "../storytale-creation/StorytaleCreation";
import Profile from "../profile/Profile";
import Rankings from "../rankings/Rankings";

import "./Tabs.scss";

const Tabs = () => (
  <IonTabs>
    <IonRouterOutlet>
      <Route exact path="/tabs/home" component={StorytaleList} />
      <Route exact path="/tabs/create" component={StorytaleCreation} />
      <Route exact path="/tabs/rankings" component={Rankings} />
      <Route exact path="/tabs/profile" component={Profile} />
      <Redirect exact from="/tabs" to="/tabs/home" />
    </IonRouterOutlet>
    <IonTabBar slot="bottom" className="custom-tab-bar">
      <IonTabButton tab="home" href="/tabs/home">
        <IonIcon icon={library} />
        <IonLabel>Galería</IonLabel>
      </IonTabButton>
      <IonTabButton tab="create" href="/tabs/create">
        <IonIcon icon={addCircle} />
        <IonLabel>Crear</IonLabel>
      </IonTabButton>
      <IonTabButton tab="rankings" href="/tabs/rankings">
        <IonIcon icon={trophyOutline} />
        <IonLabel>Rankings</IonLabel>
      </IonTabButton>
      <IonTabButton tab="profile" href="/tabs/profile">
        <IonIcon icon={person} />
        <IonLabel>Perfil</IonLabel>
      </IonTabButton>
    </IonTabBar>
  </IonTabs>
);

export default Tabs;
