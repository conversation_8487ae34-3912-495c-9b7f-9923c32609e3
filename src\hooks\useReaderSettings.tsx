import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Interfaz para las configuraciones del lector
export interface ReaderSettings {
  readingSpeed: number;  // 0.5 - 2.0
  fontSize: number;      // 0.8 - 1.5
}

// Configuraciones por defecto
const DEFAULT_SETTINGS: ReaderSettings = {
  readingSpeed: 1.0,
  fontSize: 1.0,
};

// Clave para localStorage
const SETTINGS_STORAGE_KEY = 'story_reader_settings';

// Contexto para las configuraciones
interface ReaderSettingsContextType {
  settings: ReaderSettings;
  updateReadingSpeed: (speed: number) => void;
  updateFontSize: (size: number) => void;
  resetSettings: () => void;
  applyFontSizeStyles: () => void;
}

const ReaderSettingsContext = createContext<ReaderSettingsContextType | null>(null);

// Provider para las configuraciones
interface ReaderSettingsProviderProps {
  children: ReactNode;
}

export const ReaderSettingsProvider = ({ children }: ReaderSettingsProviderProps) => {
  // Cargar configuraciones desde localStorage
  const loadSettings = (): ReaderSettings => {
    try {
      const saved = localStorage.getItem(SETTINGS_STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        // Validar que los valores estén en rangos correctos
        return {
          readingSpeed: Math.max(0.5, Math.min(2.0, parsed.readingSpeed || DEFAULT_SETTINGS.readingSpeed)),
          fontSize: Math.max(0.8, Math.min(1.5, parsed.fontSize || DEFAULT_SETTINGS.fontSize)),
        };
      }
    } catch (error) {
      console.warn('Error loading reader settings:', error);
    }
    return DEFAULT_SETTINGS;
  };

  const [settings, setSettings] = useState<ReaderSettings>(loadSettings);

  // Guardar configuraciones en localStorage cuando cambien
  useEffect(() => {
    try {
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('Error saving reader settings:', error);
    }
  }, [settings]);

  // Aplicar fontSize dinámicamente usando CSS custom properties
  useEffect(() => {
    // Aplicar la variable CSS al root del documento
    document.documentElement.style.setProperty('--story-font-size', `${settings.fontSize * 1.4}rem`);
  }, [settings.fontSize]);

  // Funciones para actualizar configuraciones
  const updateReadingSpeed = (speed: number) => {
    const clampedSpeed = Math.max(0.5, Math.min(2.0, speed));
    setSettings(prev => ({ ...prev, readingSpeed: clampedSpeed }));
  };

  const updateFontSize = (size: number) => {
    const clampedSize = Math.max(0.8, Math.min(1.5, size));
    setSettings(prev => ({ ...prev, fontSize: clampedSize }));
  };

  const resetSettings = () => {
    setSettings(DEFAULT_SETTINGS);
  };

  // Función para reaplicar estilos de fontSize manualmente
  const applyFontSizeStyles = () => {
    // Aplicar la variable CSS al root del documento
    document.documentElement.style.setProperty('--story-font-size', `${settings.fontSize * 1.4}rem`);
  };

  const value: ReaderSettingsContextType = {
    settings,
    updateReadingSpeed,
    updateFontSize,
    resetSettings,
    applyFontSizeStyles,
  };

  return (
    <ReaderSettingsContext.Provider value={value}>
      {children}
    </ReaderSettingsContext.Provider>
  );
};

// Hook para usar las configuraciones
export const useReaderSettings = () => {
  const context = useContext(ReaderSettingsContext);
  if (!context) {
    throw new Error('useReaderSettings must be used within a ReaderSettingsProvider');
  }
  return context;
};
