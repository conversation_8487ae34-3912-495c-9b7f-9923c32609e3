import { Redirect, Route } from "react-router-dom";
import { IonApp, setupIonicReact } from "@ionic/react";
import { IonReactRouter } from "@ionic/react-router";
import { Fullscreen } from "@boengli/capacitor-fullscreen";
import { App as CapacitorApp } from "@capacitor/app";

/* Core CSS required for Ionic components to work properly */
import "@ionic/react/css/core.css";

/* Basic CSS for apps built with Ionic */
import "@ionic/react/css/normalize.css";
import "@ionic/react/css/structure.css";
import "@ionic/react/css/typography.css";

/* Optional CSS utils that can be commented out */
import "@ionic/react/css/padding.css";
import "@ionic/react/css/float-elements.css";
import "@ionic/react/css/text-alignment.css";
import "@ionic/react/css/text-transformation.css";
import "@ionic/react/css/flex-utils.css";
import "@ionic/react/css/display.css";

// import "@ionic/react/css/palettes/dark.system.css"; // Comentado para usar nuestro sistema personalizado

/* Theme variables */
// React
import { useState, useEffect } from "react";

// Capacitor / Plugins
import { Capacitor, PluginListenerHandle } from "@capacitor/core";

// CSS / SCSS
import "./theme/variables.scss";
import "./App.scss";
import "./theme/popover-fix.css";

// Hooks personalizados
import { AuthProvider, useAuth } from "./hooks/useAuth";
import { FirebaseProvider } from "./hooks/useFirebase";
import { useUser } from "./hooks/useUser";
import { BusyProvider, useBusy } from "./hooks/useBusy";
import { ThemeProvider } from "./hooks/useTheme";

// Contextos / Providers
import { ToastProvider } from "./contexts/ToastContext";
import { ReaderSettingsProvider } from "./hooks/useReaderSettings";
import { ScoreProvider } from "./hooks/score";

// Componentes
import Tabs from "./components/tabs/Tabs";
import StoryReader from "./components/story-reader/StoryReader";
import Login from "./components/login/Login";
import CreateAccount from "./components/createAccount/CreateAccount";
import EmailVerification from "./components/emailVerification/EmailVerification";
import AuthGuard from "./components/authGuard/AuthGuard";
import Spinner from "./components/spinners/Spinner";
import AppWelcomeScreen from "./components/appWelcomeScreen/AppWelcomeScreen";

setupIonicReact();

const _App: React.FC = () => {
  const { user } = useUser();
  const { loading } = useAuth();
  const { isBusy } = useBusy();


  const [showWelcome, setShowWelcome] = useState(true);



  useEffect(() => {
  // Solo ejecutar en mobile
  if (Capacitor.getPlatform() === "android" || Capacitor.getPlatform() === "ios") {
    const enableFullscreen = async () => {
      try {
        await Fullscreen.activateImmersiveMode();
        console.log("Fullscreen enabled");
      } catch (error) {
        console.error("Error enabling fullscreen:", error);
      }
    };

    // Al iniciar
    enableFullscreen();

    let listener: PluginListenerHandle | undefined;

    (async () => {
      listener = await CapacitorApp.addListener("resume", enableFullscreen);
    })();

    return () => {
      if (listener && listener.remove) {
        listener.remove();
      }
    };
  }
}, []);

  if (loading || showWelcome) {
    return (
      <AppWelcomeScreen
        onFinish={() => setShowWelcome(false)}
        minDuration={3000}
      />
    );
  }

  return (
    <IonApp>
      <IonReactRouter>
        {!user ? (
          <>
            <Route exact path="/login">
              <Login />
            </Route>
            <Route exact path="/register">
              <CreateAccount />
            </Route>
            <Route exact path="*">
              <Redirect to="/login" />
            </Route>
          </>
        ) : (
          <AuthGuard>
            <Route path="/tabs">
              <Tabs />
            </Route>
            <Route exact path="/email-verification">
              <EmailVerification />
            </Route>
            <Route path="/story-reader">
              <StoryReader />
            </Route>
            <Route exact path="/">
              <Redirect to="/tabs/home" />
            </Route>
            <Route exact path="*">
              <Redirect to="/tabs/home" />
            </Route>
          </AuthGuard>
        )}
      </IonReactRouter>
      {isBusy && <Spinner />}
    </IonApp>
  );
};



const App = () => {
  return (
    <FirebaseProvider>
      <AuthProvider>
        <BusyProvider>
          <ToastProvider>
            <ScoreProvider>
                <ReaderSettingsProvider>
                  <ThemeProvider>
                    <_App />
                  </ThemeProvider>
                </ReaderSettingsProvider>
            </ScoreProvider>
          </ToastProvider>
        </BusyProvider>
      </AuthProvider>
    </FirebaseProvider>
  );
};
export default App;
