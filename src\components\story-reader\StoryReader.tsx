import {
  <PERSON><PERSON>ontent,
  IonIcon,
  Ion<PERSON>enu<PERSON>utton,
  <PERSON><PERSON><PERSON>t,
  Ion<PERSON>utton,
  IonSpinner,
} from "@ionic/react";
import { TextToSpeech } from "@capacitor-community/text-to-speech";
import {
  caretForwardCircleOutline,
  settings,
  refresh,
  pause,
  moon,
  sunny,
  closeOutline,
} from "ionicons/icons";

import "./StoryReader.scss";
import { useState, useEffect, useRef } from "react";
import ConfigStoryReader from "../config-story-reader/ConfigStoryReader";
import { useLocation, useHistory } from "react-router";
import { IStorytale } from "../../common/models/storytale.interface";
import { useTheme } from "../../hooks/useTheme";
import { useReaderSettings } from "../../hooks/useReaderSettings";
import { Capacitor } from "@capacitor/core";
import { useScoreActions } from "../../hooks/score";

interface TranslationTooltip {
  phrase: string;
  translation: string;
  position: { x: number; y: number };
}

export default function StoryReader() {
  const [tooltip, setTooltip] = useState<TranslationTooltip | null>(null);
  const [isLoadingTranslation, setIsLoadingTranslation] = useState(false);
  const [playbackState, setPlaybackState] = useState<'stopped' | 'playing' | 'paused'>('stopped');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [ttsNotAvailable, setTtsNotAvailable] = useState(false);

  const location = useLocation();
  const history = useHistory();
  const { story } = location.state as { story: IStorytale };
  const { theme, toggleTheme } = useTheme();
  const { settings: readerSettings, applyFontSizeStyles } = useReaderSettings();
  const { addStoryCompletionPoints } = useScoreActions();
  const overlayRef = useRef<HTMLDivElement>(null);
  const playbackStateRef = useRef<'stopped' | 'playing' | 'paused'>('stopped');

  // Aplicar configuraciones de fontSize cuando el componente se monta
  useEffect(() => {
    // Aplicar inmediatamente al montar el componente
    applyFontSizeStyles();
  }, []); // Solo se ejecuta al montar el componente

  if (!story) {
    // Si no hay cuento, redirigimos de nuevo a la galería
    history.push("/");
    return null;
  }

  // Función para obtener el código de idioma para TextToSpeech
  const getLanguageCode = (language: string): string => {
    const languageMap: { [key: string]: string } = {
      english: "en-US",
      spanish: "es-ES",
      french: "fr-FR",
      german: "de-DE",
      italian: "it-IT",
      portuguese: "pt-PT",
      russian: "ru-RU",
      japanese: "ja-JP",
      chinese: "zh-CN",
    };
    const langKey = language.toLowerCase();
    return languageMap[langKey] || "en-US";
  };

  // Función para verificar si TTS está disponible
  const isTTSAvailable = (): boolean => {
    const platform = Capacitor.getPlatform();
    if (platform === "android" || platform === "ios") {
      return true; // TTS nativo disponible en mobile
    }
    // En web, verificar si Speech API está disponible
    return 'speechSynthesis' in window;
  };

  // Función para manejar click en frases con posicionamiento inteligente
  const handlePhraseClick = (phrase: string, event: React.MouseEvent) => {
    const translation = story.translatedPhrases.find(tp => tp.original === phrase);
    if (!translation) return;

    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const isMobile = window.innerWidth <= 768;
    const tooltipWidth = isMobile ? Math.min(320, window.innerWidth - 40) : 320;
    const tooltipHeight = 150; // Alto aproximado del tooltip
    const margin = isMobile ? 20 : 20; // Margen de seguridad

    // Calcular posición X (horizontal)
    let x = rect.left + rect.width / 2 - tooltipWidth / 2; // Centrar horizontalmente

    // Ajustar si se sale por la izquierda
    if (x < margin) {
      x = margin;
    }

    // Ajustar si se sale por la derecha
    if (x + tooltipWidth > window.innerWidth - margin) {
      x = window.innerWidth - tooltipWidth - margin;
    }

    // Calcular posición Y (vertical)
    let y = rect.top - tooltipHeight - 10; // Por defecto arriba del elemento

    // Si no hay espacio arriba, mostrar abajo
    if (y < margin) {
      y = rect.bottom + 10;
    }

    // Si tampoco hay espacio abajo, centrar verticalmente
    if (y + tooltipHeight > window.innerHeight - margin) {
      y = Math.max(margin, (window.innerHeight - tooltipHeight) / 2);
    }

    // En móvil, asegurar que esté siempre visible
    if (isMobile) {
      y = Math.max(margin, Math.min(y, window.innerHeight - tooltipHeight - margin));
      x = Math.max(margin, Math.min(x, window.innerWidth - tooltipWidth - margin));
    }

    setTooltip({
      phrase: phrase,
      translation: translation.translated,
      position: { x, y }
    });
  };

  // Función para cerrar tooltip
  const closeTooltip = () => {
    setTooltip(null);
  };

  // Función para reproducir una frase individual
  const playPhrase = async (phrase: string) => {
    try {
      // Verificar si TTS está disponible antes de intentar reproducir
      if (!isTTSAvailable()) {
        console.warn('TTS no está disponible en esta plataforma');
        return;
      }

      await TextToSpeech.speak({
        text: phrase,
        lang: getLanguageCode(story.language),
        rate: readerSettings.readingSpeed,
        pitch: 1.0,
        volume: 1.0,
        category: "ambient",
        queueStrategy: 0, // Cambiar a 0 para reproducción continua
      });
    } catch (error) {
      console.error('Error al reproducir la frase:', error);
    }
  };

  // Función para manejar play/pause del cuento
  const handlePlayPause = (): void => {
    if (playbackState === 'playing') {
      pauseStory();
    } else if (playbackState === 'paused') {
      resumeStory();
    } else {
      startStory();
    }
  };

  // Función para iniciar la reproducción
  const startStory = async (): Promise<void> => {
    setPlaybackState('playing');
    playbackStateRef.current = 'playing';
    setCurrentPhraseIndex(0);
    await playFromIndex(0);
  };

  // Función para reproducir desde un índice específico de forma fluida pero sincronizada
  const playFromIndex = async (startIndex: number): Promise<void> => {
    try {
      // Verificar si TTS está disponible antes de intentar reproducir
      if (!isTTSAvailable()) {
        console.warn('TTS no está disponible en esta plataforma');
        setTtsNotAvailable(true);
        setPlaybackState('stopped');
        playbackStateRef.current = 'stopped';
        return;
      }

      for (let i = startIndex; i < story.translatedPhrases.length; i++) {
        // Verificar si se pausó o detuvo
        if (playbackStateRef.current === 'paused' || playbackStateRef.current === 'stopped') {
          setCurrentPhraseIndex(i);
          return;
        }

        // Actualizar el índice visual ANTES de reproducir
        setCurrentPhraseIndex(i);

        // Reproducir la frase actual con configuración para lectura fluida
        await TextToSpeech.speak({
          text: story.translatedPhrases[i].original,
          lang: getLanguageCode(story.language),
          rate: readerSettings.readingSpeed,
          pitch: 1.0,
          volume: 1.0,
          category: "ambient",
          queueStrategy: 0, // Sin cola para reproducción inmediata
        });
      }

      // Si llegó al final, resetear y otorgar puntos
      if (playbackStateRef.current === 'playing') {
        setCurrentPhraseIndex(0);
        setPlaybackState('stopped');
        playbackStateRef.current = 'stopped';

        // Otorgar puntos por completar el cuento
        await addStoryCompletionPoints(story.title);
      }
    } catch (error) {
      console.error('Error al reproducir TTS:', error);
      setPlaybackState('stopped');
      playbackStateRef.current = 'stopped';
    }
  };



  const pauseStory = (): void => {
    // Solo intentar detener TTS si está disponible
    if (isTTSAvailable()) {
      TextToSpeech.stop();
    }
    setPlaybackState('paused');
    playbackStateRef.current = 'paused';
    // El currentPhraseIndex se mantiene para saber dónde reanudar
  };

  const resumeStory = async (): Promise<void> => {
    setPlaybackState('playing');
    playbackStateRef.current = 'playing';
    // Reanudar desde donde se pausó
    await playFromIndex(currentPhraseIndex);
  };

  const resetStory = (): void => {
    // Solo intentar detener TTS si está disponible
    if (isTTSAvailable()) {
      TextToSpeech.stop();
    }
    setCurrentPhraseIndex(0);
    setPlaybackState('stopped');
    playbackStateRef.current = 'stopped';
  };

  // Función para renderizar el contenido con frases clickeables (todas de corrido)
  const renderClickableContent = () => {
    return (
      <div className="story-paragraph">
        {story.translatedPhrases.map((phraseObj, index) => (
          <span
            key={index}
            className={`story-sentence ${
              (playbackState === 'playing' || playbackState === 'paused') && index === currentPhraseIndex
                ? 'currently-playing'
                : ''
            }`}
            onClick={(e) => handlePhraseClick(phraseObj.original, e)}
          >
            {phraseObj.original}
          </span>
        ))}
      </div>
    );
  };

  // Efecto para cerrar tooltip al hacer click fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltip && overlayRef.current) {
        const tooltipElement = overlayRef.current.querySelector('.modern-tooltip');
        if (tooltipElement && !tooltipElement.contains(event.target as Node)) {
          closeTooltip();
        }
      }
    };

    if (tooltip) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [tooltip]);



  return (
    <>
      <ConfigStoryReader />
      <IonContent className="container-story-reader" id="main-content">
        <header className="header-story-reader">
          <div className="header-spacer"></div>
          <IonButton
            fill="clear"
            onClick={toggleTheme}
            className="theme-toggle-btn"
            title="Cambiar tema"
          >
            <IonIcon icon={theme === "dark" ? sunny : moon} slot="icon-only" />
          </IonButton>
        </header>
        <div className="text-container">

          {ttsNotAvailable && (
            <div className="tts-warning">
              <IonIcon icon={caretForwardCircleOutline} />
              Audio no disponible en esta plataforma
            </div>
          )}
          <IonText color="primary">
            <h1 className="title-story-reader">{story.title}</h1>
          </IonText>
          <h3 className="subtitle-story-reader">
            {story.difficulty} - {story.language}
          </h3>
          <div className="text-story">
            {renderClickableContent()}
          </div>
        </div>
        <footer className="footer-story-reader">
          <div className="reset-audio">
            <IonIcon onClick={resetStory} icon={refresh}></IonIcon>
          </div>
          <div className="play">
            {playbackState === 'stopped' && (
              <IonIcon
                className="icon-play"
                onClick={handlePlayPause}
                icon={caretForwardCircleOutline}
                title="Reproducir cuento"
              ></IonIcon>
            )}
            {playbackState === 'playing' && (
              <IonIcon
                className="icon-play"
                onClick={handlePlayPause}
                icon={pause}
                title="Pausar cuento"
              ></IonIcon>
            )}
            {playbackState === 'paused' && (
              <IonIcon
                className="icon-play"
                onClick={handlePlayPause}
                icon={caretForwardCircleOutline}
                title="Continuar cuento"
              ></IonIcon>
            )}
          </div>
          <IonMenuButton autoHide={false} className="config">
            <IonIcon icon={settings}></IonIcon>
          </IonMenuButton>
        </footer>

        {/* Tooltip de traducción */}
        {tooltip && (
          <div className="translation-overlay" ref={overlayRef}>
            <div
              className="modern-tooltip"
              style={{
                position: 'fixed',
                left: `${tooltip.position.x}px`,
                top: `${tooltip.position.y}px`,
                zIndex: 10000
              }}
            >
              <div className="translation-content">
                <div className="translation-header">
                  <div className="original-sentence">{tooltip.phrase}</div>
                  <div className="header-buttons">
                    <IonButton
                      fill="clear"
                      onClick={() => playPhrase(tooltip.phrase)}
                      className="play-phrase-btn"
                      title="Reproducir frase"
                    >
                      <IonIcon icon={caretForwardCircleOutline} />
                    </IonButton>
                    <IonButton
                      fill="clear"
                      onClick={closeTooltip}
                      className="close-translation-btn"
                    >
                      <IonIcon icon={closeOutline} />
                    </IonButton>
                  </div>
                </div>
                <div className="translation-text">
                  {isLoadingTranslation ? (
                    <div className="loading-container">
                      <IonSpinner name="crescent" />
                      <span>Traduciendo...</span>
                    </div>
                  ) : (
                    tooltip.translation
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </IonContent>
    </>
  );
}
