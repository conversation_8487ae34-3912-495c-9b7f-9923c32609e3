import {
  IonButton,
  IonInput,
  IonText,
  IonIcon,
  IonSpinner,
  IonPage,
  IonContent,
} from "@ionic/react";
import { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import "./StorytaleCreation.scss";
import { useStorytale } from "../../hooks/useStorytale";
import { ICreateStory } from "../../common/models/storytale.interface";
import {
  bookOutline,
  createOutline,
  languageOutline,
  ribbonOutline,
} from "ionicons/icons";
import { useBusy } from "../../hooks/useBusy";
import Header from "../header/Header";

const StorytaleCreation = () => {
  const history = useHistory();
  const { genres, createStory } = useStorytale();
  const { isBusy } = useBusy();

  // Form state
  const [selectedGenre, setSelectedGenre] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [selectedDifficulty, setSelectedDifficulty] = useState("");
  const [title, setTitle] = useState("");

  // Validation state
  const [titleTouched, setTitleTouched] = useState(false);
  const [genreTouched, setGenreTouched] = useState(false);
  const [languageTouched, setLanguageTouched] = useState(false);
  const [difficultyTouched, setDifficultyTouched] = useState(false);
  const [showSuccessToast, setShowSuccessToast] = useState(false);


  // Validation helpers
  const isTitleValid = title.trim() !== "";
  const isGenreValid = selectedGenre !== "";
  const isLanguageValid = selectedLanguage !== "";
  const isDifficultyValid = selectedDifficulty !== "";

  const isFormValid =
    isTitleValid && isGenreValid && isLanguageValid && isDifficultyValid;

  // Event handlers
  const handleGenreSelect = (id: number) => {
    setSelectedGenre(id.toString());
    setGenreTouched(true);
  };

  const handleLanguageSelect = (language: string) => {
    setSelectedLanguage(language);
    setLanguageTouched(true);
  };

  const handleDifficultySelect = (difficulty: string) => {
    setSelectedDifficulty(difficulty);
    setDifficultyTouched(true);
  };

  const handleTitleChange = (e: any) => {
    setTitle(e.detail.value);
    setTitleTouched(true);
  };

  const handleSubmit = async () => {
    if (!isFormValid) {
      // Mark all fields as touched to show validation errors
      setTitleTouched(true);
      setGenreTouched(true);
      setLanguageTouched(true);
      setDifficultyTouched(true);
      return;
    }

    const storyData: ICreateStory = {
      title: title,
      genre: selectedGenre,
      language: selectedLanguage,
      difficulty: selectedDifficulty,
    };

    await createStory(storyData);

    // Show success message
    setShowSuccessToast(true);

    // Reset form
    setTitle("");
    setSelectedGenre("");
    setSelectedLanguage("");
    setSelectedDifficulty("");

    // Reset validation
    setTitleTouched(false);
    setGenreTouched(false);
    setLanguageTouched(false);
    setDifficultyTouched(false);

    // Wait a moment for the toast to show, then navigate
    setTimeout(() => {
      // Navigate to the home tab in the new tabs system
      history.push({
        pathname: "/tabs/home",
        state: { refresh: true },
      });
    }, 500);
  };

  return (
    <IonPage>
      <Header />
      <IonContent>
        <div className="storytale-creation-container">
          <div className="creator-container">
            <div className="creation-header">
              <h1>Crea Tu Cuento</h1>
              <p>
                Completa los detalles a continuación para crear un cuento personalizado que
                se adapte a tus preferencias.
              </p>
            </div>

            {/* Title Section */}
            <div className="form-section">
              <div className="section-title">
                <IonIcon icon={createOutline} style={{ marginRight: "10px" }} />
                <h2>Título del Cuento</h2>
                {titleTouched && !isTitleValid && (
                  <span className="required">*</span>
                )}
              </div>

              <IonInput
                className="title-input"
                placeholder="Ingresa un título cautivador para tu cuento"
                value={title}
                onIonInput={handleTitleChange}
                onIonBlur={() => setTitleTouched(true)}
              />

              {titleTouched && !isTitleValid && (
                <div className="error-message">
                  Por favor ingresa un título para tu cuento
                </div>
              )}
            </div>

            {/* Genre Section */}
            <div className="form-section">
              <div className="section-title">
                <IonIcon icon={bookOutline} style={{ marginRight: "10px" }} />
                <h2>Género</h2>
                {genreTouched && !isGenreValid && (
                  <span className="required">*</span>
                )}
              </div>

              <IonText>
                <p>Selecciona un género que mejor se adapte a tu cuento</p>
              </IonText>

              <div className="options-grid">
                {genres.map((genre) => (
                  <IonButton
                    key={genre.id}
                    className={`option-button ${selectedGenre === genre.id.toString() ? "selected" : ""}`}
                    onClick={() => handleGenreSelect(genre.id)}
                  >
                    {genre.name}
                  </IonButton>
                ))}
              </div>

              {genreTouched && !isGenreValid && (
                <div className="error-message">Por favor selecciona un género</div>
              )}
            </div>

            {/* Language Section */}
            <div className="form-section">
              <div className="section-title">
                <IonIcon
                  icon={languageOutline}
                  style={{ marginRight: "10px" }}
                />
                <h2>Idioma</h2>
                {languageTouched && !isLanguageValid && (
                  <span className="required">*</span>
                )}
              </div>

              <IonText>
                <p>Elige el idioma para tu cuento</p>
              </IonText>

              <div className="options-grid">
                {["Inglés", "Español", "Francés", "Alemán"].map((language, index) => {
                  const originalLanguages = ["English", "Spanish", "French", "German"];
                  return (
                    <IonButton
                      key={originalLanguages[index]}
                      className={`option-button ${selectedLanguage === originalLanguages[index] ? "selected" : ""}`}
                      onClick={() => handleLanguageSelect(originalLanguages[index])}
                    >
                      {language}
                    </IonButton>
                  );
                })}
              </div>

              {languageTouched && !isLanguageValid && (
                <div className="error-message">Por favor selecciona un idioma</div>
              )}
            </div>

            {/* Difficulty Section */}
            <div className="form-section">
              <div className="section-title">
                <IonIcon icon={ribbonOutline} style={{ marginRight: "10px" }} />
                <h2>Dificultad</h2>
                {difficultyTouched && !isDifficultyValid && (
                  <span className="required">*</span>
                )}
              </div>

              <IonText>
                <p>Selecciona el nivel de dificultad de lectura</p>
              </IonText>

              <div className="options-grid">
                {["Fácil", "Medio", "Difícil"].map((difficulty, index) => {
                  const originalDifficulties = ["Easy", "Medium", "Hard"];
                  return (
                    <IonButton
                      key={originalDifficulties[index]}
                      className={`option-button ${selectedDifficulty === originalDifficulties[index] ? "selected" : ""}`}
                      onClick={() => handleDifficultySelect(originalDifficulties[index])}
                    >
                      {difficulty}
                    </IonButton>
                  );
                })}
              </div>

              {difficultyTouched && !isDifficultyValid && (
                <div className="error-message">
                  Por favor selecciona un nivel de dificultad
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="submit-container">
              <IonButton
                className={`submit-button ${isFormValid ? "enabled" : "disabled"}`}
                disabled={!isFormValid || isBusy}
                onClick={handleSubmit}
              >
                {isBusy ? (
                  <>
                    Creando Cuento
                    <IonSpinner
                      name="crescent"
                      color="secondary"
                      style={{ marginLeft: "10px" }}
                    />
                  </>
                ) : (
                  "Crear Cuento"
                )}
              </IonButton>
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default StorytaleCreation;
