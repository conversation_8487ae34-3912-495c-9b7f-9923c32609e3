/* Estilos para el componente Rankings */

.rankings-page-header {
  padding: 16px 16px 8px;
  text-align: center;
  background: var(--ion-background-color);
}

.rankings-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.user-stats-card {
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: var(--ion-card-background);
  color: var(--ion-color-dark);
}

.user-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.stat-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--ion-color-light-tint);
  border-radius: 12px;
  border: 1px solid var(--ion-color-light);

  ion-icon {
    font-size: 1.3rem;
    color: var(--ion-color-primary);
    margin-top: 2px;
    flex-shrink: 0;
  }
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--ion-color-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--ion-text-color);
}

.stat-sublabel {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--ion-color-light);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--ion-color-success), var(--ion-color-success-tint));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-success);
  min-width: 30px;
  text-align: right;
}

.rankings-container {
  padding: 8px 16px 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
}

.loading-container ion-spinner {
  --color: var(--primary-color);
  transform: scale(1.5);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
  text-align: center;
}

.error-icon {
  font-size: 4rem;
  color: var(--ion-color-danger);
  margin-bottom: 8px;
}

.error-container h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-danger);
  font-size: 1.2rem;
  font-weight: 600;
}

.error-container p {
  margin: 0 0 16px 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  line-height: 1.4;
}

.ranking-item {
  --padding-start: 20px;
  --padding-end: 20px;
  --inner-padding-end: 0;
  --background: var(--ion-item-background);
  --color: var(--ion-color-dark);
  margin-bottom: 12px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  min-height: 70px;
}

.ranking-position {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 12px;
  min-width: 40px;
}

.position-icon {
  font-size: 1.8rem;
  margin-bottom: 2px;

  &.gold {
    color: #FFD700;
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
  }

  &.silver {
    color: #C0C0C0;
    filter: drop-shadow(0 2px 4px rgba(192, 192, 192, 0.3));
  }

  &.bronze {
    color: #CD7F32;
    filter: drop-shadow(0 2px 4px rgba(205, 127, 50, 0.3));
  }

  &.default {
    color: var(--ion-color-medium);
    font-weight: 700;
  }
}

.position-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-medium);
}

.user-avatar {
  width: 48px;
  height: 48px;
  margin-right: 12px;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--ion-text-color);
}

.user-level {
  margin: 4px 0 0 0;
  font-size: 0.85rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.user-points {
  display: flex;
  align-items: center;
}

.points-badge {
  --background: var(--primary-color);
  --color: white;
  font-weight: 700;
  font-size: 1rem;
  padding: 10px 16px;
  border-radius: 20px;
  min-width: 70px;
  text-align: center;
  font-size: 0.85rem;
}

.empty-rankings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  color: var(--ion-color-light);
  margin-bottom: 16px;
}

.empty-rankings h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-medium);
  font-size: 1.1rem;
}

.empty-rankings p {
  margin: 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.mock-data-indicator {
  text-align: center;
  padding: 16px;
  margin-top: 8px;
}

.mock-data-indicator p {
  margin: 0;
  font-size: 0.85rem;
  font-style: italic;
  opacity: 0.7;
}

/* Estilos específicos para posiciones destacadas */
.ranking-item:nth-child(1) {
  --background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), var(--ion-item-background));
  border: 2px solid #FFD700;
}

.ranking-item:nth-child(2) {
  --background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), var(--ion-item-background));
  border: 2px solid #C0C0C0;
}

.ranking-item:nth-child(3) {
  --background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), var(--ion-item-background));
  border: 2px solid #CD7F32;
}

/* Responsive design */
@media (max-width: 768px) {
  .user-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.1rem;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .user-level {
    font-size: 0.75rem;
  }
  
  .points-badge {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

/* Estilos para el modal compacto */
.user-position-banner {
  background: var(--primary-color);
  color: white;
  padding: 16px;
  text-align: center;
  margin: 16px;
  border-radius: 12px;
}

.user-position-banner h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
}

.user-position-banner p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.ranking-item-compact {
  --padding-start: 12px;
  --padding-end: 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  background: white;
}

.ranking-position-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
  min-width: 60px;
}

.position-icon-compact {
  font-size: 1.2rem;
}

.user-info-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.points-badge-compact {
  --background: var(--primary-color);
  --color: white;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.modal-footer {
  padding: 16px;
}

.close-button {
  --border-color: var(--primary-color);
  --color: var(--primary-color);
}

/* Tema oscuro */
.dark-theme {
  .rankings-page-header {
    background: var(--ion-background-color);
    border-bottom-color: var(--ion-color-dark);
  }

  .rankings-title {
    color: var(--ion-color-primary);
  }

  .rankings-subtitle {
    color: var(--ion-color-medium);
  }

  .user-stats-card {
    background: var(--ion-card-background);
    color: var(--ion-text-color);
  }

  .stat-item {
    background: var(--ion-color-dark-tint);
    border-color: var(--ion-color-dark);
  }

  .progress-bar {
    background: var(--ion-color-dark);
  }

  .ranking-item {
    --background: var(--ion-item-background);
    --color: var(--ion-text-color);
  }

  .ranking-item-compact {
    --background: var(--ion-item-background);
    --color: var(--ion-text-color);
  }

  .user-name {
    color: var(--ion-text-color);
  }

  .ranking-item:nth-child(1) {
    --background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), var(--ion-item-background));
  }

  .ranking-item:nth-child(2) {
    --background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), var(--ion-item-background));
  }

  .ranking-item:nth-child(3) {
    --background: linear-gradient(135deg, rgba(205, 127, 50, 0.15), var(--ion-item-background));
  }
}
